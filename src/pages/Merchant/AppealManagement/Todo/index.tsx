import { Layout, Tabs, Pagination, Radio } from 'antd';
import AppealDetailContent from '../Detail/components/AppealDetailContent';
import { PageHeaderWrapper } from '@ant-design/pro-layout';
import { ProCard } from '@ant-design/pro-components';

const AppealListPage = () => {
    return (
        <PageHeaderWrapper>
            <ProCard>
                <Radio.Group defaultValue="a" buttonStyle="solid">
                    <Radio.Button value="a"> 停车费申诉</Radio.Button>
                    <Radio.Button value="b"> 占位费申诉</Radio.Button>
                </Radio.Group>

                <Layout
                    style={{
                        border: '1px solid #eee',
                        backgroundColor: '#fff',
                        marginTop: '16px',
                    }}
                >
                    <Layout.Sider
                        style={{
                            backgroundColor: '#fff',
                            borderRight: '1px solid #eee',
                            padding: '16px',
                        }}
                    >
                        <ul>
                            <li>1</li>
                            <li>2</li>
                        </ul>
                    </Layout.Sider>
                    <Layout.Content
                        style={{
                            backgroundColor: '#fff',
                            padding: '16px',
                        }}
                    >
                        <AppealDetailContent />
                    </Layout.Content>
                </Layout>
                <Pagination
                    style={{
                        float: 'right',
                        marginTop: '16px',
                    }}
                />
            </ProCard>
        </PageHeaderWrapper>
    );
};

export default AppealListPage;
