import ProCard from '@ant-design/pro-card';
import { LeftOutlined } from '@ant-design/icons';
import { PageHeaderWrapper } from '@ant-design/pro-layout';
import { useRequest } from 'ahooks';
import { message } from 'antd';
import { history } from 'umi';

import ServiceForm from '../components/ServiceForm';
import { addServiceApi } from '@/services/Merchant/ServiceMakertApi';
import BaseFormExample from '@/components/BaseForm/example';

const AddPage = (props: any) => {
    const { route } = props;
    const goBack = () => {
        history.goBack();
    };
    const { run, loading } = useRequest(addServiceApi, {
        manual: true,
        onSuccess: (res: any) => {
            if (res.ret === 200) {
                message.success('保存成功');
                history.goBack();
            }
        },
    });
    const submitForm = (values: any) => {
        console.debug('submit Values', values);
        run(values);
    };
    return (
        <PageHeaderWrapper
            title={
                <div className="page-title" onClick={goBack}>
                    <LeftOutlined />
                    {route.name}
                </div>
            }
        >
            <ProCard>
                <BaseFormExample />
                <ServiceForm type="ADD" onSubmit={submitForm} loading={loading} />
            </ProCard>
        </PageHeaderWrapper>
    );
};

export default AddPage;
