import { FormProps, FormItemProps, RowProps } from 'antd';
import { BaseButtonConfig } from '@/components/BaseButtons';
import React from 'react';

// 类型推导工具类型
export type InferModelValueFromConfig<T extends readonly FormConfig[]> = {
    [K in T[number]['field']]?: InferFieldType<Extract<T[number], { field: K }>>;
};

// 根据FormConfig推导字段类型（主要依赖defaultValue，element已废弃string类型）
type InferFieldType<T extends FormConfig> = T['defaultValue'] extends infer D
    ? D extends undefined
        ? any // element类型已废弃，默认为any
        : D extends string
        ? string
        : D extends number
        ? number
        : D extends boolean
        ? boolean
        : D extends any[]
        ? D
        : D
    : any;

// 基础FormConfig接口
export interface FormConfig<TField extends string = string, TValue = any> {
    field: TField;
    title: string;
    /** 建议是不带`props`的纯组件，`props`可以在`FormConfig.props`中设置,带`props`的话会被`FormConfig.props`覆盖，一些已经预处理的的属性，例如`disabled`,`maxLength`,直接在组件中传`props`无效
     * @example
     * element: Input
     * @example
     * element: <Input />
     * @example
     * element: (h) => h(Input)
     */
    element?:
        | React.ReactNode
        | React.ComponentType<any>
        | ((
              createElement: typeof React.createElement,
              item: FormConfig<TField, TValue>,
              modelValue: any,
          ) => React.ReactElement);
    defaultValue?: TValue;
    props?: any;
    rules?: any[];
    show?: boolean | ((params: any) => boolean);
    colProps?: any;
    itemProps?: FormItemProps;
    style?: React.CSSProperties;
    itemStyle?: React.CSSProperties;
    required?: boolean | 'check';
    disabled?: boolean;
    preview?: {
        element?: string;
        formatter?: (params: any, item: FormConfig<TField, TValue>) => any;
        style?: React.CSSProperties;
    };
    previewSlot?: string;
    previewFormatter?: (value: TValue, params: any, item: FormConfig<TField, TValue>) => any;
    slotName?: string;
    onlyShow?: boolean;
    groupClass?: string;
    groupEnd?: boolean;
    on?: {
        change?: (value: any, ...args: any[]) => void;
        [key: string]: any;
    };
    nativeOn?: {
        [key: string]: any;
    };
    childrenNode?: React.ReactNode | React.ReactNode[] | ((h: any) => React.ReactNode);
    brotherNodes?: React.ReactNode | React.ReactNode[] | ((h: any) => React.ReactNode);
}

export interface ItemLayout {
    span?: number;
    gutter?: number | [number, number];
}

export interface FlexLayoutMode {
    labelSpan?: number;
    contentSpan?: number;
    gutter?: number;
}

// 泛型BaseFormProps，支持类型推导
export interface BaseFormProps<
    TConfig extends readonly FormConfig[] = FormConfig[],
    TModelValue = any,
> extends Omit<FormProps, 'onFinish' | 'onFinishFailed'> {
    config?: TConfig;
    modelValue?: TModelValue;
    itemLayout?: ItemLayout;
    preview?: boolean;
    disabled?: boolean;
    isFilter?: boolean;
    filterLoading?: boolean;
    filterMaxCount?: number;
    filterButtons?: (string | BaseButtonConfig)[];
    btnsLayout?: RowProps;
    flexLayoutMode?: boolean | FlexLayoutMode;
    onUpdateModelValue?: (value: TModelValue) => void;
    onItemChange?: (field: string, value: any) => void;
    onReset?: () => void;
    onSubmit?: (params: TModelValue) => void;
    onLoadData?: (params: any) => void;
    [key: string]: any;
}

export interface BaseFormRef {
    validate: () => Promise<any>;
    validateFields: (fields?: string[]) => Promise<any>;
    resetFields: () => void;
    clearValidate: (fields?: string[]) => void;
    reset: () => void;
    submit: (params?: any) => Promise<void>;
}

// 类型工具函数
export type CreateFormConfig<T extends Record<string, any>> = {
    [K in keyof T]: FormConfig<K & string, T[K]> & {
        field: K & string;
        defaultValue?: T[K];
    };
}[keyof T][];

// 从配置创建严格类型的FormConfig
export function createFormConfig<T extends Record<string, any>>(
    config: CreateFormConfig<T>,
): CreateFormConfig<T> {
    return config;
}

// 类型推导辅助函数
export function defineFormConfig<T extends readonly FormConfig[]>(
    config: T,
): T & { readonly __modelValueType: InferModelValueFromConfig<T> } {
    return config as any;
}
