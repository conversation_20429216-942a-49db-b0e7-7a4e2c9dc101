.base-form {
  &-flex {
    display: flex;

    & > div {
      width: 100%;
    }
  }

  .form-item-row {
    width: 100%;
    flex-wrap: wrap;

    > div {
      height: 100%;
      padding-right: 8px;
    }

    .form-item-row-search {
      padding-right: 0 !important;
    }
  }

  .ant-col-24.ant-form-item-label {
    line-height: 39.999px;
  }

  .base-form-flex-item {
    .ant-form-item-label {
      padding: 0;
    }

    .ant-form-item-control {
      line-height: initial;
    }

    &.ant-form-item-required:not(.ant-form-item-no-asterisk) {
      .base-form-flex-label {
        span {
          position: relative;

          &:before {
            content: '*';
            color: #ff4d4f;
            position: absolute;
            left: -10px;
            top: 0;
          }
        }
      }
    }

    .base-form-flex-label {
      text-align: right;
      padding: 0 5px 0 10px;
    }

    .base-form-flex-content {
      padding: 0;
    }

    .base-form-flex-error {
      color: #ff4d4f;
      font-size: 12px;
      line-height: 1;
      padding-top: 2px;
      position: absolute;
      top: 0;
      left: 0;
    }
  }

  &.ant-form-inline {
    .ant-form-item-label {
      padding: 0;
    }
  }

  &-filter {
    .form-item-row-search {
      display: flex;
      align-items: flex-end;

      .ant-form-item {
        margin-bottom: 0;
      }
    }
  }
}
