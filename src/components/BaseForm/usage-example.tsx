// 使用示例：如何在实际项目中使用动态类型推导

import React, { useState } from 'react';
import { Input, InputNumber, Switch, Select } from 'antd';
import { defineTypedForm, type InferModelValueFromConfig } from './index';

// ============ 方法1: 使用 defineTypedForm（推荐） ============

// 1. 定义表单配置
const userFormDefinition = defineTypedForm([
    {
        field: 'name',
        title: '姓名',
        element: Input,
        defaultValue: '',
        required: 'check',
    },
    {
        field: 'age',
        title: '年龄',
        element: InputNumber,
        defaultValue: 18,
    },
    {
        field: 'isActive',
        title: '是否激活',
        element: Switch,
        defaultValue: true,
    },
] as const);

// 2. 导出推导出的类型（这就是您想要的效果！）
export type UserFormData = typeof userFormDefinition.FormData;

// 3. 在组件中使用
const UserFormComponent: React.FC = () => {
    const [formData, setFormData] = useState<UserFormData>(
        userFormDefinition.createInitialValue()
    );

    // formData 现在有完整的类型提示：
    // formData.name: string
    // formData.age: number  
    // formData.isActive: boolean

    return (
        <div>
            {/* 使用表单组件 */}
            {/* <BaseForm 
                config={userFormDefinition.config}
                modelValue={formData}
                onUpdateModelValue={setFormData}
            /> */}
        </div>
    );
};

// ============ 方法2: 直接使用 InferModelValueFromConfig ============

// 1. 定义配置
const productFormConfig = [
    {
        field: 'productName',
        title: '产品名称',
        element: Input,
        defaultValue: '',
    },
    {
        field: 'price',
        title: '价格',
        element: InputNumber,
        defaultValue: 0,
    },
    {
        field: 'category',
        title: '分类',
        element: Select,
        defaultValue: [] as string[],
        props: {
            mode: 'multiple',
            options: [
                { label: '电子产品', value: 'electronics' },
                { label: '服装', value: 'clothing' },
            ],
        },
    },
] as const;

// 2. 直接导出推导的类型
export type ProductFormData = InferModelValueFromConfig<typeof productFormConfig>;

// 3. 在组件中使用
const ProductFormComponent: React.FC = () => {
    const [formData, setFormData] = useState<ProductFormData>({
        productName: '',
        price: 0,
        category: [],
    });

    // formData 现在有完整的类型提示：
    // formData.productName: string
    // formData.price: number
    // formData.category: string[]

    return (
        <div>
            {/* 使用表单组件 */}
            {/* <BaseForm 
                config={productFormConfig}
                modelValue={formData}
                onUpdateModelValue={setFormData}
            /> */}
        </div>
    );
};

// ============ 方法3: 在单独的配置文件中定义 ============

// configs/userForm.ts
export const userFormConfig = [
    {
        field: 'username',
        title: '用户名',
        element: Input,
        defaultValue: '',
    },
    {
        field: 'email',
        title: '邮箱',
        element: Input,
        defaultValue: '',
    },
    {
        field: 'role',
        title: '角色',
        element: Select,
        defaultValue: 'user' as 'admin' | 'user' | 'guest',
        props: {
            options: [
                { label: '管理员', value: 'admin' },
                { label: '用户', value: 'user' },
                { label: '访客', value: 'guest' },
            ],
        },
    },
] as const;

// 导出类型
export type UserConfigFormData = InferModelValueFromConfig<typeof userFormConfig>;

// ============ 使用场景示例 ============

// 在 API 调用中使用
async function saveUser(userData: UserFormData) {
    // userData 有完整的类型检查
    const response = await fetch('/api/users', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(userData),
    });
    return response.json();
}

// 在状态管理中使用
interface UserState {
    currentUser: UserFormData | null;
    users: UserFormData[];
    loading: boolean;
}

// 在表单验证中使用
function validateUserForm(data: UserFormData): string[] {
    const errors: string[] = [];
    
    if (!data.name.trim()) {
        errors.push('姓名不能为空');
    }
    
    if (data.age < 0 || data.age > 120) {
        errors.push('年龄必须在0-120之间');
    }
    
    return errors;
}

// 在数据转换中使用
function transformUserData(formData: UserFormData) {
    return {
        ...formData,
        displayName: `${formData.name} (${formData.age}岁)`,
        status: formData.isActive ? '激活' : '未激活',
    };
}

export { UserFormComponent, ProductFormComponent };
