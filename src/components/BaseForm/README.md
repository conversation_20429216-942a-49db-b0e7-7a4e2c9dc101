# BaseForm - Umi3.5 + Antd 版本

基于 umi3.5 和 antd 的增强表单组件，支持配置化生成表单项和 TypeScript 类型推导。

## 功能特性

1. **🎯 类型安全** - 根据配置自动推导 `modelValue` 类型
2. **🔧 配置化表单** - 通过配置项生成表单项，支持自定义渲染
3. **🔍 筛选器模式** - 支持筛选器模式，无需额外使用 AutoFilter
4. **🔄 自动初始化** - 组件会自动初始化表单数据对象，无需声明初始化数据
5. **✅ 双向绑定** - 支持受控模式进行双向绑定
6. **🌳 快捷校验** - 配置项 required 设置为 'check'，即可快捷设置必填校验
7. **⚡ 多级字段** - 支持多级field设置，如 `user.profile.name`
8. **🎨 灵活布局** - 支持多种布局模式
9. **🚀 性能优化** - 使用 React.memo 和 useCallback 优化性能

## 类型推导使用方法

### 1. 基础用法

```typescript
import { BaseForm, defineFormConfig, type InferModelValueFromConfig } from '@/components/BaseForm';
import { Input, InputNumber, Switch } from 'antd';

// 定义表单配置（element不再支持字符串，使用组件）
const formConfig = defineFormConfig([
    {
        field: 'name',
        title: '姓名',
        element: Input, // 使用组件而非字符串
        defaultValue: '', // 通过defaultValue推导类型
        required: 'check',
    },
    {
        field: 'age',
        title: '年龄',
        element: InputNumber, // 使用组件而非字符串
        defaultValue: 18, // 推导为number类型
    },
    {
        field: 'isActive',
        title: '是否激活',
        element: Switch, // 使用组件而非字符串
        defaultValue: true, // 推导为boolean类型
    },
] as const); // 重要：使用 as const

// 自动推导类型
type FormData = InferModelValueFromConfig<typeof formConfig>;
// 结果：{ name: string; age: number; isActive: boolean; }
```

## Props

### BaseFormProps

| 参数               | 类型                                  | 默认值                          | 说明                                               |
| ------------------ | ------------------------------------- | ------------------------------- | -------------------------------------------------- |
| config             | `FormConfig[]`                        | `[]`                            | 表单控件配置数组                                   |
| modelValue         | `object`                              | `{}`                            | 表单参数对象                                       |
| itemLayout         | `ItemLayout`                          | `{ span: 12 }`                  | 布局对象，包含span（栅格宽度）和gutter（栅格间隔） |
| preview            | `boolean`                             | `false`                         | 是否预览模式                                       |
| disabled           | `boolean`                             | `false`                         | 是否禁用表单                                       |
| isFilter           | `boolean`                             | `false`                         | 是否启用筛选功能                                   |
| filterLoading      | `boolean`                             | `false`                         | 筛选加载状态                                       |
| filterMaxCount     | `number`                              | `7`                             | 展示筛选按钮的最大数量                             |
| filterButtons      | `(string \| ButtonConfig)[]`          | `['expand', 'reset', 'submit']` | 筛选按钮配置数组                                   |
| btnsLayout         | `object`                              | -                               | 按钮布局对象                                       |
| flexLayoutMode     | `boolean \| FlexLayoutMode`           | -                               | 是否启用弹性布局                                   |
| onUpdateModelValue | `(value: any) => void`                | -                               | 数据更新回调                                       |
| onItemChange       | `(field: string, value: any) => void` | -                               | 表单项变化回调                                     |
| onReset            | `() => void`                          | -                               | 重置回调                                           |
| onSubmit           | `(params: any) => void`               | -                               | 提交回调                                           |
| onLoadData         | `(params: any) => void`               | -                               | 加载数据回调                                       |

### FormConfig

| 参数         | 类型                                  | 说明                           |
| ------------ | ------------------------------------- | ------------------------------ |
| field        | `string`                              | 字段名，支持嵌套如 `user.name` |
| title        | `string`                              | 字段标题                       |
| element      | `string \| React.ComponentType`       | 表单元素类型或组件             |
| defaultValue | `any`                                 | 默认值                         |
| props        | `any`                                 | 传递给表单元素的属性           |
| rules        | `any[]`                               | 验证规则                       |
| show         | `boolean \| (params: any) => boolean` | 是否显示                       |
| colProps     | `any`                                 | 栅格布局属性                   |
| itemProps    | `FormItemProps`                       | 表单项属性                     |
| style        | `React.CSSProperties`                 | 样式                           |
| itemStyle    | `React.CSSProperties`                 | 表单项样式                     |
| required     | `boolean \| 'check'`                  | 是否必填，'check'为快捷必填    |
| disabled     | `boolean`                             | 是否禁用                       |
| preview      | `object`                              | 预览配置                       |
| previewSlot  | `string`                              | 预览插槽名                     |
| slotName     | `string`                              | 自定义插槽名                   |
| onlyShow     | `boolean`                             | 仅显示，不参与数据处理         |

## Ref 方法

### BaseFormRef

| 方法           | 参数                | 说明         |
| -------------- | ------------------- | ------------ |
| validate       | -                   | 验证整个表单 |
| validateFields | `fields?: string[]` | 验证指定字段 |
| resetFields    | -                   | 重置表单字段 |
| clearValidate  | `fields?: string[]` | 清除验证结果 |
| reset          | -                   | 重置表单数据 |
| submit         | `params?: any`      | 提交表单     |

## 类型推导功能

### 自动类型推导

BaseForm 支持根据配置自动推导 `modelValue` 的类型，主要依赖 `defaultValue` 进行类型推导：

| defaultValue 类型 | 推导的 TypeScript 类型 |
|------------------|---------------------|
| `string` 字面量 | `string` |
| `number` 字面量 | `number` |
| `boolean` 字面量 | `boolean` |
| `array` | 保持原数组类型 |
| `undefined` | `any` |

### 使用类型推导

#### 方法1: 使用 defineTypedForm（推荐）

```typescript
import { defineTypedForm } from '@/components/BaseForm';
import { Input, InputNumber } from 'antd';

// 1. 定义表单配置
const userFormDefinition = defineTypedForm([
    {
        field: 'name',
        title: '姓名',
        element: Input,
        defaultValue: '', // 推导为 string
    },
    {
        field: 'age',
        title: '年龄',
        element: InputNumber,
        defaultValue: 18, // 推导为 number
    },
] as const);

// 2. 导出推导出的类型（这就是您想要的效果！）
export type UserFormData = typeof userFormDefinition.FormData;

// 3. 使用
const [formData, setFormData] = useState<UserFormData>(
    userFormDefinition.createInitialValue()
);
```

#### 方法2: 直接使用 InferModelValueFromConfig

```typescript
import { type InferModelValueFromConfig } from '@/components/BaseForm';

const formConfig = [
    {
        field: 'name',
        title: '姓名',
        element: Input,
        defaultValue: '',
    },
    {
        field: 'age',
        title: '年龄',
        element: InputNumber,
        defaultValue: 18,
    },
] as const;

// 直接导出推导的类型
export type FormData = InferModelValueFromConfig<typeof formConfig>;
```

## 支持的表单元素

**注意**：`element` 属性已废弃字符串类型，请直接使用组件：

| 推荐用法 | 对应组件 | 说明 |
| -------- | -------- | ---- |
| `Input` | Input | 输入框 |
| `Input.TextArea` | TextArea | 文本域 |
| `Select` | Select | 选择器 |
| `DatePicker` | DatePicker | 日期选择器 |
| `DatePicker.RangePicker` | RangePicker | 日期范围选择器 |
| `InputNumber` | InputNumber | 数字输入框 |
| `Switch` | Switch | 开关 |
| `Checkbox` | Checkbox | 复选框 |
| `Radio` | Radio | 单选框 |
| 自定义组件 | 任意React组件 | 自定义表单元素 |

## 使用示例

### 基础表单

```tsx
import React, { useState, useRef } from 'react';
import { BaseForm } from '@/components/BaseForm';
import type { FormConfig, BaseFormRef } from '@/components/BaseForm/types';

const Example = () => {
  const formRef = useRef<BaseFormRef>(null);
  const [formData, setFormData] = useState({});

  const formConfig: FormConfig[] = [
    {
      field: 'name',
      title: '姓名',
      element: 'input',
      required: 'check',
      props: { placeholder: '请输入姓名' },
      colProps: { span: 12 },
    },
    {
      field: 'age',
      title: '年龄',
      element: 'input-number',
      props: { min: 0, max: 120 },
      colProps: { span: 12 },
    },
    {
      field: 'email',
      title: '邮箱',
      element: 'input',
      required: 'check',
      rules: [{ type: 'email', message: '请输入正确的邮箱格式' }],
      colProps: { span: 24 },
    },
  ];

  const handleSubmit = async () => {
    try {
      await formRef.current?.validate();
      console.log('表单数据:', formData);
    } catch (error) {
      console.log('验证失败:', error);
    }
  };

  return (
    <BaseForm
      ref={formRef}
      config={formConfig}
      modelValue={formData}
      onUpdateModelValue={setFormData}
    />
  );
};
```

### 筛选表单

```tsx
const filterConfig: FormConfig[] = [
  {
    field: 'keyword',
    title: '关键词',
    element: 'input',
    props: { placeholder: '请输入关键词' },
  },
  {
    field: 'status',
    title: '状态',
    element: 'select',
    props: {
      placeholder: '请选择状态',
      options: [
        { label: '全部', value: '' },
        { label: '启用', value: 'active' },
        { label: '禁用', value: 'inactive' },
      ],
    },
  },
  // 更多配置...
];

<BaseForm
  config={filterConfig}
  modelValue={filterData}
  onUpdateModelValue={setFilterData}
  isFilter={true}
  filterMaxCount={4}
  onSubmit={(data) => console.log('查询:', data)}
  onReset={() => console.log('重置')}
  onLoadData={(params) => console.log('加载数据:', params)}
/>;
```

### 自定义表单元素

```tsx
const customConfig: FormConfig[] = [
  {
    field: 'customField',
    title: '自定义字段',
    element: ({ value, onChange }) => (
      <div>
        <input value={value} onChange={(e) => onChange(e.target.value)} />
        <span>自定义组件</span>
      </div>
    ),
  },
];
```

### 条件显示

```tsx
const conditionalConfig: FormConfig[] = [
  {
    field: 'type',
    title: '类型',
    element: 'select',
    props: {
      options: [
        { label: '个人', value: 'personal' },
        { label: '企业', value: 'company' },
      ],
    },
  },
  {
    field: 'companyName',
    title: '公司名称',
    element: 'input',
    show: (params) => params.type === 'company',
  },
];
```

### 嵌套字段

```tsx
const nestedConfig: FormConfig[] = [
  {
    field: 'user.profile.name',
    title: '用户姓名',
    element: 'input',
  },
  {
    field: 'user.profile.age',
    title: '用户年龄',
    element: 'input-number',
  },
];
```

## 注意事项

1. 确保项目已安装 `antd` 和 `lodash-es` 依赖
2. 确保项目支持 CSS Modules (umi3.5 默认支持)
3. 表单配置项的 `field` 字段必须唯一
4. 嵌套字段支持格式：`parent.child.grandchild`
5. 不能使用数字作为字段名的首位标识
6. 筛选模式下会自动添加展开/收起、重置、查询按钮


