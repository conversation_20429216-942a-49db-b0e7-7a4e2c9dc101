import React, { useState, useRef } from 'react';
import { Card, Space, Switch, InputNumber, Button, Input, Select, DatePicker } from 'antd';
import { Group } from 'antd/lib/radio';
import { BaseForm,defineTypedForm } from './index';
import type { FormConfig, BaseFormRef, } from './types';

const Example: React.FC = () => {
    const formRef = useRef<BaseFormRef>(null);
    const [formData, setFormData] = useState({
        name: '',
        age: 18,
        email: '',
        gender: '',
        city: '',
        description: '',
        isActive: true,
    });
    const [disabled, setDisabled] = useState(false);
    const [isFilter, setIsFilter] = useState(false);

    // 基础表单配置
    const basicFormConfig: FormConfig[] = [
        {
            field: 'name',
            title: '姓名',
            element: Input,
            required: 'check',
            props: {
                placeholder: '请输入姓名',
            },
            colProps: { span: 12 },
        },
        {
            field: 'isActive',
            title: '是否激活',
            element: Switch,
            colProps: { span: 12 },
        },
        {
            field: 'age',
            title: '年龄',
            element: InputNumber,
            props: {
                min: 0,
                max: 120,
                placeholder: '请输入年龄',
            },
            colProps: { span: 12 },
        },
        {
            field: 'email',
            title: '邮箱',
            element: Input,
            required: 'check',
            props: {
                placeholder: '请输入邮箱',
            },
            rules: [
                {
                    type: 'email',
                    message: '请输入正确的邮箱格式',
                },
            ],
            show: (params) => params.isActive === true,
            colProps: { span: 12 },
        },
        {
            field: 'gender',
            title: '性别',
            element: Select,
            props: {
                placeholder: '请选择性别',
                options: [
                    { label: '男', value: 'male' },
                    { label: '女', value: 'female' },
                ],
            },
            colProps: { span: 12 },
            show: () => formData.isActive === true,
        },
        {
            field: 'city',
            title: '城市',
            element: Select,
            props: {
                placeholder: '请选择城市',
                options: [
                    { label: '北京', value: 'beijing' },
                    { label: '上海', value: 'shanghai' },
                    { label: '广州', value: 'guangzhou' },
                    { label: '深圳', value: 'shenzhen' },
                ],
            },
            colProps: { span: 12 },
        },
        {
            field: 'description',
            title: '描述',
            element: Input.TextArea,
            props: {
                placeholder: '请输入描述',
                rows: 4,
            },
            colProps: {
                span: 24,
            },
            itemProps: {
                labelCol: { span: 8 },
                wrapperCol: { span: 16 },
            },
        },
    ];

    
    // 筛选表单配置
    const userFormDefinition = defineTypedForm([
        {
            field: 'keyword',
            title: '关键词',
            element: Input,
            props: {
                placeholder: '请输入关键词',
            },
        },
        {
            field: 'status',
            title: '状态',
            element: Select,
            props: {
                placeholder: '请选择状态',
                options: [
                    { label: '全部', value: '' },
                    { label: '启用', value: 'active' },
                    { label: '禁用', value: 'inactive' },
                ],
            },
        },
        {
            field: 'createTime',
            title: '创建时间',
            element: <DatePicker.RangePicker placeholder={['开始时间2', '结束时间2']} />,
            props: {
                format: 'YYYY-MM-DD',
            },
            show: () => (filterData as any)?.status === 'active',
        },
        {
            field: 'category',
            title: '分类',
            element: <Group />,
            props: {
                placeholder: '请选择分类',
                options: [
                    { label: '分类1', value: 'cat1' },
                    { label: '分类2', value: 'cat2' },
                    { label: '分类3', value: 'cat3' },
                ],
            },
        },
        {
            field: 'priority',
            title: '优先级',
            element: Select,
            props: {
                placeholder: '请选择优先级',
                options: [
                    { label: '高', value: 'high' },
                    { label: '中', value: 'medium' },
                    { label: '低', value: 'low' },
                ],
            },
        },
        {
            field: 'tags',
            title: '标签',
            props: {
                placeholder: '请输入标签',
            },
        },
        {
            field: 'owner',
            title: '负责人',
            props: {
                placeholder: '请输入负责人',
            },
        },
        {
            field: 'department',
            title: '部门',
            element: Select,
            props: {
                placeholder: '请选择部门',
                options: [
                    { label: '技术部', value: 'tech' },
                    { label: '产品部', value: 'product' },
                    { label: '运营部', value: 'operation' },
                ],
            },
        },
    ]);
    const { FormData, createInitialValue,config : filterFormConfig } = userFormDefinition;

    const [filterData, setFilterData] = useState<typeof FormData>(); 
    const handleSubmit = (data: any) => {
        console.log('表单提交:', data);
    };

    const handleReset = () => {
        console.log('表单重置');
    };

    const handleLoadData = (params: any) => {
        console.log('加载数据:', params);
    };

    const handleValidate = async () => {
        try {
            await formRef.current?.validate();
            console.log('验证通过');
        } catch (error) {
            console.log('验证失败:', error);
        }
    };

    return (
        <div style={{ padding: 24 }}>
            <Space direction="vertical" size="large" style={{ width: '100%' }}>
                <Card title="基础表单" size="small">
                    <BaseForm
                        ref={formRef}
                        config={basicFormConfig}
                        modelValue={formData}
                        onUpdateModelValue={setFormData}
                        disabled={disabled}
                        labelCol={{ span: 8 }}
                        wrapperCol={{ span: 16 }}
                    />
                    <div style={{ marginTop: 16 }}>
                        <Button type="primary" onClick={handleValidate}>
                            验证表单
                        </Button>
                        <Button style={{ marginLeft: 8 }} onClick={() => formRef.current?.reset()}>
                            重置表单
                        </Button>
                    </div>
                </Card>

                <Card title="筛选表单" size="small">
                    <BaseForm
                        config={filterFormConfig}
                        modelValue={filterData}
                        itemLayout={{ span: 6, gutter: 8 }}
                        onUpdateModelValue={setFilterData}
                        isFilter={isFilter}
                        filterMaxCount={7}
                        onSubmit={handleSubmit}
                        onReset={handleReset}
                        onLoadData={handleLoadData}
                    />
                </Card>

                <Card title="控制面板" size="small">
                    <Space>
                        <span>禁用状态:</span>
                        <Switch checked={disabled} onChange={setDisabled} />

                        <span>筛选模式:</span>
                        <Switch checked={isFilter} onChange={setIsFilter} />
                    </Space>
                </Card>

                {/* <Card title="基础表单数据" size="small">
                    <pre>{JSON.stringify(formData, null, 2)}</pre>
                </Card>

                <Card title="筛选表单数据" size="small">
                    <pre>{JSON.stringify(filterData, null, 2)}</pre>
                </Card> */}
            </Space>
        </div>
    );
};

export default Example;
