# BaseModal - Umi3.5 + Antd 版本

基于 umi3.5 和 antd 的模态框组件，从原 Vue 版本迁移而来。

## 功能特性

1. **配置化模态框** - 通过配置项进行模态框和表单渲染
2. **集成表单功能** - 内置 BaseForm 组件，支持复杂表单
3. **自定义按钮** - 支持自定义模态框按钮配置
4. **Loading 状态** - 支持模态框和表单的 loading 状态
5. **异步数据加载** - 支持异步获取初始数据
6. **表单验证** - 集成 antd 表单验证功能

## Props

### BaseModalProps

| 参数 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| destroyOnClose | `boolean` | `false` | 关闭时销毁模态框内容 |
| modalConfig | `ModalConfig \| ModalConfig[]` | - | 模态框配置 |
| formConfig | `FormConfig[]` | `[]` | 表单配置 |
| submit | `(params: any) => Promise<any> \| any` | - | 提交处理函数 |
| closed | `() => void` | - | 关闭回调 |
| modelValue | `any` | `{}` | 表单数据 |
| type | `string` | `'default'` | 模态框类型 |
| modalBtns | `ButtonConfig[] \| (() => ButtonConfig[])` | - | 自定义按钮配置 |
| btnsLayout | `object` | - | 按钮布局配置 |
| showSubmitButton | `boolean` | `true` | 是否显示提交按钮 |
| onParamsChanged | `(params: any) => void` | - | 参数变化回调 |

### ModalConfig

| 参数 | 类型 | 说明 |
|------|------|------|
| type | `string` | 模态框类型标识 |
| props | `ModalProps` | antd Modal 组件属性 |
| formConfig | `FormConfig[]` | 表单配置 |
| formProps | `object` | 表单属性 |

## Ref 方法

### BaseModalRef

| 方法 | 参数 | 说明 |
|------|------|------|
| open | `options?` | 打开模态框 |
| close | - | 关闭模态框 |
| handleOk | `options?` | 处理确定操作 |
| dynamicForm | - | 表单实例引用 |

#### open 方法参数

```typescript
{
  params?: any;           // 初始参数
  openFn?: () => Promise<any>; // 异步获取数据函数
  otherParams?: any;      // 其他参数
}
```

#### handleOk 方法参数

```typescript
{
  submitFn?: (params: any) => Promise<any>; // 自定义提交函数
  isValid?: boolean;      // 是否验证表单
  extendParams?: any;     // 扩展参数
}
```

## 使用示例

### 基础用法

```tsx
import React, { useRef } from 'react';
import { Button } from 'antd';
import { BaseModal } from '@/components/BaseModal';
import type { BaseModalRef, FormConfig } from '@/components/BaseModal/types';

const Example = () => {
  const modalRef = useRef<BaseModalRef>(null);

  const formConfig: FormConfig[] = [
    {
      field: 'name',
      title: '姓名',
      element: 'input',
      required: 'check',
      props: { placeholder: '请输入姓名' },
    },
    {
      field: 'age',
      title: '年龄',
      element: 'input-number',
      props: { min: 0, max: 120 },
    },
  ];

  const handleSubmit = async (params: any) => {
    console.log('提交数据:', params);
    // 处理提交逻辑
    return undefined; // 返回 undefined 会自动关闭模态框
  };

  const openModal = () => {
    modalRef.current?.open({
      params: { name: '', age: 18 },
    });
  };

  return (
    <>
      <Button onClick={openModal}>打开模态框</Button>
      <BaseModal
        ref={modalRef}
        modalConfig={{
          type: 'default',
          props: { title: '用户信息', width: 600 },
          formConfig,
        }}
        submit={handleSubmit}
      />
    </>
  );
};
```

### 异步数据加载

```tsx
const openEditModal = () => {
  modalRef.current?.open({
    openFn: async () => {
      // 异步获取数据
      const response = await fetch('/api/user/1');
      return response.json();
    },
  });
};
```

### 多类型模态框

```tsx
const modalConfigs = [
  {
    type: 'add',
    props: { title: '新增用户' },
    formConfig,
  },
  {
    type: 'edit',
    props: { title: '编辑用户' },
    formConfig,
  },
  {
    type: 'view',
    props: { title: '查看用户' },
    formConfig,
    formProps: { preview: true },
  },
];

<BaseModal
  ref={modalRef}
  modalConfig={modalConfigs}
  type="add" // 指定使用哪个配置
  submit={handleSubmit}
/>
```

### 自定义按钮

```tsx
const customButtons = [
  {
    label: '取消',
    event: () => modalRef.current?.close(),
    props: { type: 'default' },
  },
  {
    label: '保存并继续',
    event: async () => {
      await modalRef.current?.handleOk({ isValid: true });
      // 继续其他操作
    },
    props: { type: 'primary' },
  },
];

<BaseModal
  ref={modalRef}
  modalBtns={customButtons}
  showSubmitButton={false}
  // ...其他props
/>
```

## 注意事项

1. 确保项目已安装 `antd` 和 `lodash-es` 依赖
2. 确保项目支持 CSS Modules (umi3.5 默认支持)
3. 表单配置项的 `field` 字段必须唯一
4. 提交函数返回 `undefined` 会自动关闭模态框
5. 支持嵌套字段，如 `user.profile.name`

## 迁移说明

从 Vue 版本迁移到 React 版本的主要变化：

1. **框架变更**: Vue → React + TypeScript
2. **UI 库变更**: Element Plus → Antd
3. **状态管理**: Vue 响应式 → React Hooks
4. **样式**: Vue scoped CSS → CSS Modules
5. **事件处理**: Vue 事件 → React 回调函数
