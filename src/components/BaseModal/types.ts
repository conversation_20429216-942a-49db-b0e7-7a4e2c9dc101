import { ModalProps } from 'antd';
import { BaseButtonConfig } from '../BaseButtons/types';

export interface FormConfig {
    field: string;
    title: string;
    element?: string | React.ComponentType<any>;
    defaultValue?: any;
    props?: any;
    rules?: any[];
    show?: boolean | ((params: any) => boolean);
    colProps?: any;
    itemProps?: any;
    style?: React.CSSProperties;
    required?: boolean | 'check';
    preview?: {
        element?: string;
        formatter?: (params: any, item: FormConfig) => any;
        style?: React.CSSProperties;
    };
    previewSlot?: string;
    slotName?: string;
    onlyShow?: boolean;
}

export interface ModalConfig {
    type: string;
    props?: ModalProps;
    formConfig?: FormConfig[];
    formProps?: {
        disabled?: boolean;
        preview?: boolean;
        [key: string]: any;
    };
}

export interface BaseModalProps {
    destroyOnClose?: boolean;
    modalConfig?: ModalConfig | ModalConfig[];
    formConfig?: FormConfig[];
    submit?: (params: any) => Promise<any> | any;
    closed?: () => void;
    modelValue?: any;
    type?: string;
    modalBtns?: BaseButtonConfig[] | (() => BaseButtonConfig[]);
    btnsLayout?: {
        type?: 'flex';
        align?: 'top' | 'middle' | 'bottom';
        justify?: 'start' | 'end' | 'center' | 'space-around' | 'space-between';
    };
    showSubmitButton?: boolean;
    onParamsChanged?: (params: any) => void;
    [key: string]: any;
}

export interface BaseModalRef {
    open: (options?: {
        params?: any;
        openFn?: () => Promise<any>;
        otherParams?: any;
    }) => Promise<void>;
    close: () => void;
    handleOk: (options?: {
        submitFn?: (params: any) => Promise<any>;
        isValid?: boolean;
        extendParams?: any;
    }) => Promise<void>;
    dynamicForm: any;
}
