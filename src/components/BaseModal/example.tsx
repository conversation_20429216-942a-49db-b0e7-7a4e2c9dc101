import React, { useState, useRef } from 'react';
import { Card, Space, Button, message } from 'antd';
import { BaseModal } from './index';
import type { BaseModalRef, FormConfig, ModalConfig } from './types';

const Example: React.FC = () => {
    const modalRef = useRef<BaseModalRef>(null);
    const [modalData, setModalData] = useState({});

    // 表单配置
    const formConfig: FormConfig[] = [
        {
            field: 'name',
            title: '姓名',
            element: 'input',
            required: 'check',
            props: {
                placeholder: '请输入姓名',
            },
        },
        {
            field: 'age',
            title: '年龄',
            element: 'input-number',
            props: {
                min: 0,
                max: 120,
                placeholder: '请输入年龄',
            },
        },
        {
            field: 'email',
            title: '邮箱',
            element: 'input',
            required: 'check',
            props: {
                placeholder: '请输入邮箱',
            },
            rules: [
                {
                    type: 'email',
                    message: '请输入正确的邮箱格式',
                },
            ],
        },
        {
            field: 'gender',
            title: '性别',
            element: 'select',
            props: {
                placeholder: '请选择性别',
                options: [
                    { label: '男', value: 'male' },
                    { label: '女', value: 'female' },
                ],
            },
        },
        {
            field: 'description',
            title: '描述',
            element: 'textarea',
            props: {
                placeholder: '请输入描述',
                rows: 4,
            },
        },
    ];

    // 模态框配置
    const modalConfig: ModalConfig = {
        type: 'default',
        props: {
            title: '用户信息',
            width: 600,
        },
        formConfig,
        formProps: {
            labelCol: { span: 6 },
            wrapperCol: { span: 18 },
        },
    };

    // 多类型模态框配置
    const multiModalConfig: ModalConfig[] = [
        {
            type: 'add',
            props: {
                title: '新增用户',
                width: 600,
            },
            formConfig,
        },
        {
            type: 'edit',
            props: {
                title: '编辑用户',
                width: 600,
            },
            formConfig,
        },
        {
            type: 'view',
            props: {
                title: '查看用户',
                width: 600,
            },
            formConfig,
            formProps: {
                preview: true,
            },
        },
    ];

    // 提交处理
    const handleSubmit = async (params: any) => {
        console.log('提交数据:', params);

        // 模拟API调用
        await new Promise((resolve) => setTimeout(resolve, 1000));

        message.success('保存成功');
        setModalData(params);

        // 返回undefined会自动关闭模态框
        return undefined;
    };

    // 模拟异步获取数据
    const fetchUserData = async () => {
        await new Promise((resolve) => setTimeout(resolve, 800));
        return {
            name: '张三',
            age: 25,
            email: '<EMAIL>',
            gender: 'male',
            description: '这是一个测试用户',
        };
    };

    const openAddModal = () => {
        modalRef.current?.open({
            params: {
                name: '',
                age: 18,
                email: '',
                gender: '',
                description: '',
            },
        });
    };

    const openEditModal = () => {
        modalRef.current?.open({
            openFn: fetchUserData,
        });
    };

    const openViewModal = () => {
        modalRef.current?.open({
            openFn: fetchUserData,
        });
    };

    const openCustomModal = () => {
        modalRef.current?.open({
            params: {
                name: '李四',
                age: 30,
            },
            otherParams: {
                customField: 'custom value',
            },
        });
    };

    return (
        <div style={{ padding: 24 }}>
            <Space direction="vertical" size="large" style={{ width: '100%' }}>
                <Card title="基础模态框示例" size="small">
                    <Space>
                        <Button type="primary" onClick={openAddModal}>
                            新增用户
                        </Button>
                        <Button onClick={openEditModal}>编辑用户（异步加载）</Button>
                        <Button onClick={openViewModal}>查看用户（预览模式）</Button>
                        <Button onClick={openCustomModal}>自定义参数</Button>
                    </Space>
                </Card>

                <Card title="模态框数据" size="small">
                    <pre>{JSON.stringify(modalData, null, 2)}</pre>
                </Card>

                <Card title="使用说明" size="small">
                    <ul>
                        <li>支持配置化模态框和表单</li>
                        <li>支持异步数据加载</li>
                        <li>支持预览模式</li>
                        <li>支持自定义按钮</li>
                        <li>支持表单验证</li>
                        <li>支持loading状态</li>
                    </ul>
                </Card>
            </Space>

            <BaseModal
                ref={modalRef}
                modalConfig={modalConfig}
                submit={handleSubmit}
                destroyOnClose={true}
                onParamsChanged={(params) => console.log('参数变化:', params)}
            />
        </div>
    );
};

export default Example;
