import React, {
    useState,
    useRef,
    useImperativeHandle,
    forwardRef,
    useMemo,
    useEffect,
} from 'react';
import { Modal, Spin } from 'antd';
import { cloneDeep, isEqual } from 'lodash';
import { BaseButtons, BaseButtonConfig } from '../BaseButtons';
import { BaseForm } from '../BaseForm';
import { initParams } from './utils';
import { BaseModalProps, BaseModalRef, ModalConfig } from './types';

/**
 * @name BaseModal
 * @description 模态框组件，基于umi3.5+antd，主要功能：
 * 1. 支持配置化模态框
 * 2. 集成表单功能
 * 3. 支持自定义按钮
 * 4. 支持loading状态
 */
const BaseModal = forwardRef<BaseModalRef, BaseModalProps>(
    (
        {
            destroyOnClose = false,
            modalConfig,
            formConfig = [],
            submit = () => {},
            closed,
            modelValue = {},
            type = 'default',
            modalBtns,
            btnsLayout = {
                type: 'flex',
                align: 'middle',
                justify: 'end',
            },
            showSubmitButton = true,
            onParamsChanged,
            children,
            ...restProps
        },
        ref,
    ) => {
        const [visible, setVisible] = useState(false);
        const [params, setParams] = useState<any>({});
        const [modalData, setModalData] = useState<any>({});
        const [loading, setLoading] = useState(false);
        const dynamicFormRef = useRef<any>(null);

        // 计算配置
        const modalConfigMap = useMemo(() => {
            const configArr = Array.isArray(modalConfig)
                ? modalConfig
                : [
                      {
                          type: 'default',
                          props: { title: 'add', ...modalConfig?.props },
                          ...(modalConfig || {}),
                      },
                  ];

            return configArr.reduce((acc, item) => {
                acc[item.type] = cloneDeep(item);
                return acc;
            }, {} as Record<string, ModalConfig>);
        }, [modalConfig]);

        const config = useMemo(() => {
            const config = modalConfigMap[type || 'default'] || {};
            config.formConfig = formConfig;
            return config;
        }, [modalConfigMap, type, formConfig]);

        // 计算按钮
        const shouldShowSubmit = useMemo(
            () => !config?.formProps?.disabled && !config?.formProps?.preview && showSubmitButton,
            [config, showSubmitButton],
        );

        const cpModalBtns = useMemo(() => {
            let btns = typeof modalBtns === 'function' ? modalBtns() : modalBtns;

            if (!btns?.length) {
                btns = [
                    {
                        label: '取消',
                        onClick: close,
                        props: { type: 'default' },
                    },
                    ...(shouldShowSubmit
                        ? [
                              {
                                  label: '确定',
                                  onClick: () => handleOk(),
                                  props: { type: 'primary' },
                              },
                          ]
                        : []),
                ] as BaseButtonConfig[];
            }
            return btns;
        }, [modalBtns, shouldShowSubmit]);

        // 监听modelValue变化
        useEffect(() => {
            if (!isEqual(modelValue, params)) {
                setParams(modelValue);
            }
        }, [modelValue]);

        // 监听params变化
        useEffect(() => {
            if (params && onParamsChanged) {
                onParamsChanged(params);
            }
        }, [params, onParamsChanged]);

        // 方法
        const open = async ({ params: openParams = {}, openFn, otherParams } = {}) => {
            if (openFn) {
                setVisible(true);
                setLoading(true);
                try {
                    const data = await openFn();
                    setModalData(data);
                    setParams({
                        ...initParams(config.formConfig, data || {}),
                        ...otherParams,
                    });
                } finally {
                    setLoading(false);
                }
            } else {
                setVisible(true);
                setParams(initParams(config.formConfig, { ...openParams, ...otherParams }, true));
            }

            setTimeout(() => {
                dynamicFormRef.current?.clearValidate?.();
            }, 100);
        };

        const handleOk = async ({ submitFn, isValid = true, extendParams = {} } = {}) => {
            try {
                if (isValid) {
                    await dynamicFormRef.current?.validate();
                }

                setLoading(true);
                const result = await (submitFn || submit)({
                    ...params,
                    ...extendParams,
                });

                if (result === undefined) close();
            } catch (e) {
                handleValidationError(e);
            } finally {
                setLoading(false);
            }
        };

        const close = () => {
            setVisible(false);
            dynamicFormRef.current?.clearValidate?.();
            closed?.();
        };

        const handleValidationError = (error: any) => {
            // 滚动到第一个错误字段
            setTimeout(() => {
                const errorElement = document.querySelector('.ant-form-item-has-error');
                if (errorElement) {
                    errorElement.scrollIntoView({ behavior: 'smooth' });
                }
            }, 100);
        };

        // 暴露方法
        useImperativeHandle(ref, () => ({
            open,
            close,
            handleOk,
            dynamicForm: dynamicFormRef.current,
        }));

        return (
            <Modal
                className={`buse-modal ${loading ? 'hidden' : ''}`}
                {...config.props}
                visible={visible}
                onCancel={close}
                destroyOnClose={destroyOnClose}
                footer={
                    <BaseButtons
                        btns={cpModalBtns}
                        row={params}
                        layout={btnsLayout}
                        buttonSpan={0}
                    />
                }
                {...restProps}
            >
                <Spin spinning={loading}>
                    <div className="buse-modal-content">
                        {children || (
                            <BaseForm
                                ref={dynamicFormRef}
                                config={config.formConfig}
                                modelValue={params}
                                onUpdateModelValue={setParams}
                                className="buse-modal-main"
                                {...config.formProps}
                            />
                        )}
                    </div>
                </Spin>
            </Modal>
        );
    },
);

BaseModal.displayName = 'BaseModal';

export default BaseModal;
