import { cloneDeep } from 'lodash-es';
import { FormConfig } from './types';

/**
 * 根据字段获取值
 */
export function deepGet(params: any, field: string): any {
  if (!field || !params) return undefined;
  
  const parts = field.split('.');
  let current = params;
  
  for (let i = 0; i < parts.length; i++) {
    if (current == null) return undefined;
    current = current[parts[i]];
  }
  
  return current;
}

/**
 * 根据字段设置值
 */
export function deepSet(params: any, field: string, value: any): void {
  const parts = field.split('.');

  if (!isNaN(Number(parts[0]))) {
    console.error(`不可使用数字作为首位标识: ${field}`);
    return;
  }
  
  let current = params;
  for (let i = 0; i < parts.length - 1; i++) {
    const part = parts[i];
    const nextPart = parts[i + 1];
    if (!current[part]) {
      // 如果是数字，表示数组索引
      if (!isNaN(Number(nextPart))) {
        current[part] = [];
      } else {
        current[part] = {};
      }
    }
    current = current[part];
  }

  // 最后一个部分赋值
  const lastPart = parts[parts.length - 1];
  current[lastPart] = value;
  
  // 强行覆盖第2级，避免无法重新渲染
  if (parts.length > 1) {
    params[parts[0]] = cloneDeep(params[parts[0]]);
  }
}

/**
 * 初始化表单参数
 */
export function initParams(
  list: FormConfig[] = [], 
  initialParams: any = {}, 
  hasOtherParams = false
): any {
  let result: any = {};
  const showList = list.filter((x) => !x?.onlyShow);
  
  result = showList
    .filter((x) => {
      if (!x.field) {
        console.error(`请配置表单项 field 或设置 onlyShow : true`, x, x.title);
        return false;
      }
      if (x.field?.includes('.') && !/^[a-zA-Z0-9]+(\.[a-zA-Z0-9]+)*$/.test(x.field)) {
        console.error(`索引标识格式有误:${x.field} `);
        return false;
      }
      const parts = x.field.split('.');
      if (!isNaN(Number(parts[0]))) {
        console.error(`不可使用数字作为首位标识: ${x.field}`);
      }
      return true;
    })
    .reduce((acc, x) => {
      deepSet(
        acc,
        x.field,
        (initialParams && deepGet(initialParams, x.field)) ??
          x?.props?.defaultValue ??
          x?.defaultValue
      );
      return acc;
    }, {});

  if (initialParams && hasOtherParams) {
    result = { ...result, ...initialParams };
  }
  
  return result;
}

/**
 * 检查是否为React节点
 */
export function isReactNode(obj: any): boolean {
  return obj && typeof obj === 'object' && obj.$$typeof;
}
